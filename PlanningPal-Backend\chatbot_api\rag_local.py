import os
import json
import hashlib
import sys
from pathlib import Path
import boto3 # type: ignore


## langchain importings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import PyPDFLoader
from langchain_aws import BedrockEmbeddings, BedrockLLM # type: ignore
from langchain_qdrant import QdrantVectorStore # type: ignore
from langchain.chains import RetrievalQA
from langchain.prompts import PromptTemplate
from langchain.chains import ConversationalRetrievalChain
# from langchain.vectorstores import QdrantVectorStore

from dotenv import load_dotenv
import os

load_dotenv()
##qdrant importings
from qdrant_client import QdrantClient # type: ignore
from qdrant_client.http import models # type: ignore
from langchain_qdrant import QdrantVectorStore # type: ignore

from fuzzywuzzy import fuzz  
import json

# AWS SES setup
AWS_REGION = os.getenv("AWS_REGION", "us-east-1")
AWS_ACCESS_KEY = os.getenv("AWS_ACCESS_KEY")
AWS_SECRET_KEY = os.getenv("AWS_SECRET_KEY")
BASE_DIR = os.path.dirname(__file__)

## path variables
DOCS_PATH = os.path.join(BASE_DIR, "Primary_Documents")
QDRANT_PATH = os.path.join(BASE_DIR, "qdrant_db")
COLLECTION_NAME = "primary_docs_kb"
METADATA_FILE = os.path.join(BASE_DIR, "embedded_metadata.json")

def correct_typos(text: str) -> str:
    correction_prompt = (
        f"Correct any typos or misspellings in this sentence:\n\n'{text}'\n\n"
        "Return only the corrected sentence without any explanation."
    )
    correction_llm = BedrockLLM(
        model_id="meta.llama3-70b-instruct-v1:0",  # or whichever smaller instruct model you prefer
        client=boto3.client("bedrock-runtime", region_name=AWS_REGION,
                            aws_access_key_id=AWS_ACCESS_KEY, aws_secret_access_key=AWS_SECRET_KEY),
        model_kwargs={'max_gen_len': 100, 'temperature': 0.0}
    )
    corrected_text = correction_llm.invoke(correction_prompt).strip()
    return corrected_text if corrected_text else text

QA_PROMPT = """
Human: Answer the question using the information provided in the <context> and the previous conversation history.

IMPORTANT: If the question contains pronouns (it, this, that, they, etc.) or references to previous topics, 
use the chat history to understand what the user is referring to.

Previous conversation: {chat_history}.Based on user's sentiment, provide a response that is appropriate for the sentiment generated from knowledge base.

- Responses must be precise, accurate, and based strictly on the context.
- Do not infer or fabricate any information.
- Keep the response under 50 words.
- CRITICAL: Format all text for narrow vertical display:
  - NEVER exceed 35-40 characters per line
  - ALWAYS manually insert line breaks (\n) after 35-40 characters
  - Break every sentence into multiple short lines
  - Use multiple paragraphs with extra line breaks between them
- If the context is not helpful or is empty, answer using general knowledge.
- When presenting lists:
  - Place each bullet point (•) on its own line
  - Keep bullet points short (35-40 characters max)
  - Add line breaks within bullet points if needed
  - Add extra spacing between bullet points
- If providing steps, place each numbered step on its own line

Special cases:

- If the question is a greeting (e.g., "hi", "hello", "hello there"), respond with:
  "Hi, I'm here to assist you with\nhouseholder planning applications\nfor Newark and Sherwood District\nCouncil."

- If the answer is not available in the context, respond with:
"I'm sorry, I don't have enough\ninformation to answer that\nspecific question right now.\n\nI recommend:\n\n• Visiting the Planning Portal\n  at: https://www.planningportal\n.co.uk, or contact Newark and\n  Sherwood District Council's\n  planning team directly\n\nThey can provide the most\naccurate and up-to-date guidance."

User sentiment: {sentiment}
<context>
{context}
</context>

Question: {question}

Assistant:
"""

def extract_document_metadata(pdf_path):
    return {
        "council": "Newark and Sherwood Council",
        "category": "House holder planning application",
        "title": Path(pdf_path).stem,
        "hash": md5_file(str(pdf_path.resolve()))
    }

# client = QdrantClient(path=QDRANT_PATH)
embeddings = BedrockEmbeddings(
        model_id="amazon.titan-embed-text-v1",
        client=boto3.client("bedrock-runtime", region_name=AWS_REGION,
                            aws_access_key_id=AWS_ACCESS_KEY, aws_secret_access_key=AWS_SECRET_KEY)
                            )

def load_faqs_to_qdrant():
    """Load FAQs into Qdrant as payloads"""
    
    # Load FAQ JSON file
    faq_file = os.path.join(BASE_DIR, "FAQs.json")
    
    if not os.path.exists(faq_file):
        print("faqs.json not found, skipping FAQ loading")
        return
    
    with open(faq_file, 'r', encoding='utf-8') as f:
        faqs = json.load(f)
    
    # Create FAQ collection if it doesn't exist
    FAQ_COLLECTION = "faqs_collection"
    
    try:
        QdrantClient.get_collection(FAQ_COLLECTION)
        print("FAQ collection already exists")
    except:
        QdrantClient.create_collection(
            collection_name=FAQ_COLLECTION,
            vectors_config=models.VectorParams(size=1, distance=models.Distance.DOT)
        )
        print("Created new FAQ collection")
    
    # Upload FAQs as payloads
    points = []
    for i, faq in enumerate(faqs):
        points.append({
            "id": i,
            "vector": [0.0],  # Dummy vector
            "payload": {
                "question": faq["question"],
                "answer": faq["answer"],
                "category": faq["metadata"]["category"],
                "tags": faq["metadata"]["tags"],
                "faq_id": faq["id"]
            }
        })
    
    QdrantClient.upsert(collection_name=FAQ_COLLECTION, points=points)
    print(f"Loaded {len(faqs)} FAQs to Qdrant")

def search_faqs(query, threshold=70):
    """Search FAQs using fuzzy matching"""
    FAQ_COLLECTION = "faqs_collection"
    
    try:
        # Get all FAQ points
        result = QdrantClient.scroll(
            collection_name=FAQ_COLLECTION,
            limit=100,
            with_payload=True
        )
        
        best_match = None
        best_score = 0
        
        for point in result[0]:
            faq_question = point.payload["question"]
            
            # Use fuzzy matching
            score = fuzz.ratio(query.lower(), faq_question.lower())
            
            if score > threshold and score > best_score:
                best_score = score
                best_match = point.payload
        
        return best_match, best_score
    
    except Exception as e:
        print(f"Error searching FAQs: {e}")
        return None, 0

def create_kb():
    client = QdrantClient(path=QDRANT_PATH)
    docs = []
    for pdf in Path(DOCS_PATH).rglob("*.pdf"):
        try:
            metadata = extract_document_metadata(pdf)
            loaded_docs = PyPDFLoader(str(pdf)).load()
            for doc in loaded_docs:
                doc.metadata.update(metadata)
            docs.extend(loaded_docs)
        except Exception as e:
            print(f"Error loading {pdf}: {e}")

    # splits = RecursiveCharacterTextSplitter(chunk_size=2000, chunk_overlap=200)  
    # splitter_docs= splits.split_documents(docs) 

    if COLLECTION_NAME in [c.name for c in client.get_collections().collections]:
        client.delete_collection(COLLECTION_NAME)
    client.create_collection(
        collection_name=COLLECTION_NAME,
        vectors_config=models.VectorParams(size=1536, distance=models.Distance.COSINE)
    )

    create_vectorstore(client)
    return client

def setup_qdrant_kb():
        # import shutil
        # if not os.path.exists(QDRANT_PATH):
        #     shutil.rmtree(QDRANT_PATH,ignore_errors=True)
    """
    Sets up or reuses the Qdrant knowledge base.
    Only creates a new one if it doesn't exist or is empty.
    """
    try:
        client = QdrantClient(path=QDRANT_PATH)
        collections = client.get_collections().collections
        collection_names = [c.name for c in collections]
        
        if COLLECTION_NAME not in collection_names:
            print("Collection not found, creating new knowledge base...")
            return create_kb()
            
        collection_info = client.get_collection(COLLECTION_NAME)
        if collection_info.points_count == 0:
            print("Collection is empty, creating new knowledge base...")
            return create_kb()
            
        print("Using existing knowledge base...")
        return client
    except Exception as e:
        print(f"Error accessing knowledge base: {e}")
        print("Creating new knowledge base...")
        return create_kb()

def create_vectorstore(client):
    return QdrantVectorStore(client=client, collection_name=COLLECTION_NAME, embedding=embeddings)

def initialize_llm(vectorstore):
    llm = BedrockLLM(
        model_id="meta.llama3-70b-instruct-v1:0",
        client=boto3.client("bedrock-runtime", region_name=AWS_REGION,
                            aws_access_key_id=AWS_ACCESS_KEY, aws_secret_access_key=AWS_SECRET_KEY),
        model_kwargs={'max_gen_len': 512, 'temperature': 0.1}
    )
    prompt = PromptTemplate(template=QA_PROMPT, input_variables=["context", "question","sentiment"])
    return ConversationalRetrievalChain.from_llm(
        llm=llm,
        retriever=vectorstore.as_retriever(search_type="similarity", search_kwargs={"k": 3}),
        return_source_documents=True,
        combine_docs_chain_kwargs={"prompt": prompt}
    )

def detect_sentiment(text):
    """
    Dummy sentiment detection using LLM itself.
    """
    sentiment_prompt = f"What is the sentiment of this message: '{text}'?\nRespond with one of: Positive, Neutral, Negative."
    sentiment_llm = BedrockLLM(
        model_id="meta.llama3-70b-instruct-v1:0",  # lightweight model
        client=boto3.client("bedrock-runtime", region_name=AWS_REGION,
                            aws_access_key_id=AWS_ACCESS_KEY, aws_secret_access_key=AWS_SECRET_KEY),
        model_kwargs={'max_gen_len': 10, 'temperature': 0.0}
    )
    return sentiment_llm.invoke(sentiment_prompt).strip()

# def handle_query(query, qa_chain, chat_history=None):
#     if chat_history is None:
#         chat_history = []
#     corrected_query = correct_typos(query)
#     sentiment = detect_sentiment(corrected_query)
#     result = qa_chain.invoke({"question": corrected_query, "chat_history": chat_history,"sentiment": sentiment})
#     chat_history.append((corrected_query, result["answer"]))
#     return result["answer"], chat_history 

def handle_query(query, qa_chain, chat_history=None):
    if chat_history is None:
        chat_history = []

    corrected_query = correct_typos(query)
    sentiment = detect_sentiment(corrected_query)

        # NEW: Check FAQs first
    faq_match, faq_score = search_faqs(corrected_query)
    
    if faq_match and faq_score > 70:
        # Use FAQ answer
        answer = faq_match["answer"]
        print(f"FAQ Match found (score: {faq_score}): {faq_match['question']}")
    else:
        # Fall back to regular RAG
        sentiment = detect_sentiment(corrected_query)

    # Convert the dictionary format to tuple format for the QA chain
    formatted_chat_history = []
    for i in range(0, len(chat_history), 2):
        if i + 1 < len(chat_history):
            user_msg = chat_history[i]
            assistant_msg = chat_history[i + 1]
            if user_msg.get("role") == "user" and assistant_msg.get("role") == "assistant":
                formatted_chat_history.append((user_msg.get("content", ""), assistant_msg.get("content", "")))

    # Get response from QA chain
    result = qa_chain.invoke({
        "question": corrected_query,
        "chat_history": formatted_chat_history,
        "sentiment": sentiment
    })

    # Extract answer from result
    if isinstance(result, dict):
        answer = result.get("answer", "")
        if not answer:
            answer = result.get("content", "")
        if not answer:
            answer = str(result)
    else:
        answer = str(result)

    # Update chat history with new exchange
    new_chat_history = chat_history.copy()  # Create a copy to avoid modifying the original
    new_chat_history.append({"role": "user", "content": corrected_query})
    new_chat_history.append({"role": "assistant", "content": answer})

    return answer, new_chat_history

def incremental_reindex():
    """
    1) Scans DOCS_PATH for PDFs,
    2) Hashes each PDF and compares with METADATA_FILE,
    3) Embeds only new/changed PDFs,
    4) Merges partial index with existing knowledge base (QDRANT_PATH),
    5) Updates METADATA_FILE so old PDFs aren't re-embedded next time.
    Returns dict containing a status message and the updated PDF list.
    """
    # 1) Load or create the embedded_files.json
    if os.path.isfile(METADATA_FILE):
        with open(METADATA_FILE, "r", encoding="utf-8") as f:
            embedded_info = json.load(f)
    else:
        embedded_info = {}

    # 2) Gather all *.pdf in data folder
    all_pdfs = list(Path(DOCS_PATH).rglob("*.pdf"))
    if not all_pdfs:
        return {"message": "No PDFs found in data folder.", "updated_pdfs": []}

    # 3) Determine which PDFs are new or changed
    new_or_changed_pdfs = []
    for pdf_path in all_pdfs:
        pdf_str = str(pdf_path.resolve())
        current_hash = md5_file(pdf_str)
        old_metadata = embedded_info.get(pdf_str, {})
        old_hash = old_metadata.get("hash")
        if current_hash != old_hash:
            new_or_changed_pdfs.append(pdf_path)

    if not new_or_changed_pdfs:
        return {"message": "No new or changed PDFs. Already up to date.", "updated_pdfs": []}
    
    # 4) Instantiate Qdrant client and ensure collection exists
    client = QdrantClient(path=QDRANT_PATH)
    
    # Ensure collection exists
    if COLLECTION_NAME not in [c.name for c in client.get_collections().collections]:
        client.create_collection(
            collection_name=COLLECTION_NAME,
            vectors_config=models.VectorParams(size=1536, distance=models.Distance.COSINE)
        )
    print(f"Starting to embed {len(new_or_changed_pdfs)} new/changed PDFs...")       

    # 5) Embed new/changed PDFs into partial index
    embed_new_pdfs(new_or_changed_pdfs, client, embeddings)
    print("Finished embedding PDFs, updating metadata")

    # 6) Update hashes in metadata
    for pdf in new_or_changed_pdfs:
        pdf_str = str(pdf.resolve())
        embedded_info[pdf_str] = extract_document_metadata(pdf)

    with open(METADATA_FILE, "w", encoding="utf-8") as f:
        json.dump(embedded_info, f, indent=2)

    return {
        "message": "Reindex complete. Embedded new/changed PDFs.",
        "updated_pdfs": [str(x) for x in new_or_changed_pdfs]
    }
    

def embed_new_pdfs(pdf_paths, client, embeddings):
    """
    Loads and embeds the new/changed PDFs, then uploads them to Qdrant with batching
    """
    docs = []
    # Process PDFs in smaller chunks
    BATCH_SIZE = 5  # Process 5 PDFs at a time
    for i in range(0, len(pdf_paths), BATCH_SIZE):
        batch = pdf_paths[i:i + BATCH_SIZE]
        for pdf_file in batch:
            try:
                loader = PyPDFLoader(str(pdf_file))
                loaded_docs = loader.load()
                # Filter to only the relevant doc(s) from that folder
                relevant_docs = [
                    d for d in loaded_docs
                    if pdf_file.name in d.metadata.get("source", "")
                ]
                docs.extend(relevant_docs)
            except Exception as e:
                print(f"Error processing {pdf_file}: {e}")
                continue

    # Split with smaller chunks for better performance
    splits = RecursiveCharacterTextSplitter(
        chunk_size=2000,  # Reduced from 10000
        chunk_overlap=200,  # Reduced from 1000
        length_function=len,
        separators=["\n\n", "\n", " ", ""]
    )
    splitter_docs = splits.split_documents(docs)

    # Create vector store
    vector_store = QdrantVectorStore(
        client=client,
        collection_name=COLLECTION_NAME,
        embedding=embeddings
    )

    # Upload documents in batches
    BATCH_UPLOAD_SIZE = 100  # Upload 100 documents at a time
    for i in range(0, len(splitter_docs), BATCH_UPLOAD_SIZE):
        batch = splitter_docs[i:i + BATCH_UPLOAD_SIZE]
        vector_store.add_documents(batch)
        print(f"Uploaded batch {i//BATCH_UPLOAD_SIZE + 1} of {(len(splitter_docs) + BATCH_UPLOAD_SIZE - 1)//BATCH_UPLOAD_SIZE}")

def md5_file(file_path: str) -> str:
    """
    Returns MD5 of the given file to detect changes.
    """
    h = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            h.update(chunk)
    return h.hexdigest()

'''
def main():
    client = setup_qdrant_kb()
    print(incremental_reindex())
    vectorstore = create_vectorstore(client)
    qa_chain = initialize_llm(vectorstore)
    chat_history = []
    while True:
        query = input("\nEnter query (or 'exit'): ")
        if query.lower() == 'exit':
            client.close()
            print("Goodbye!")
            sys.exit(0)
        answer, chat_history = handle_query(query, qa_chain, chat_history)
        print(answer)
        #handle_query(query, qa_chain)
'''
#Example usage:
if __name__ == "__main__":
    # Setup knowledge base
    qdrant_client = setup_qdrant_kb()
    
    # NEW: Load FAQs
    load_faqs_to_qdrant()
    
    # Create vector store instance
    vectorstore = create_vectorstore()
    
    # Initialize LLM chain
    qa_chain = initialize_llm(vectorstore)
    
    print("Knowledge base setup complete!")


