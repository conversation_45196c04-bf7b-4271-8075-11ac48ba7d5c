from datetime import UTC, datetime
from fastapi import FastAPI, UploadFile, File, HTTPException, Header, Depends, Form
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional, List
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON><PERSON>ultipart
from database_code.database import engine, Base, SessionLocal
from database_code.models import Users, UserSessions, UserApplicationDetails, UserApplicationStatus, CouncilApplicationMap, ApplicationTypes, UserInputs, FieldMaster, TypeMaster, PreFlightMaster, SectionMaster, CouncilDetails, CouncilComments
from typing import Annotated
from dotenv import load_dotenv
import os
from botocore.exceptions import ClientError
import boto3 # type: ignore
from math import ceil
import uuid
import json
from core.config import s3_client, S3_BUCKET_NAME
from models.request_models import DocumentValidationRequest
from models.response_models import ValidationResult, IndividualValidation
from core.validator import validate, validate_multiple
from utils import orientation, ocr_extraction, segmentation
import requests
import tempfile
import shutil
from urllib.parse import urlparse
from io import BytesIO
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, KeepTogether
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors
from reportlab.pdfgen import canvas
from PyPDF2 import PdfMerger

# Database configuration
Base.metadata.create_all(bind=engine)

app = FastAPI(debug=True)

load_dotenv()
AWS_ACCESS_KEY = os.getenv('AWS_ACCESS_KEY')
AWS_SECRET_KEY = os.getenv('AWS_SECRET_KEY')
AWS_REGION_S3 = os.getenv('AWS_REGION_S3')
S3_BUCKET_NAME = os.getenv('S3_BUCKET_NAME')
SMTP_SENDER_EMAIL = os.getenv('SMTP_SENDER_EMAIL')
SMTP_CLIENT = boto3.client('ses', region_name=os.getenv('AWS_REGION'))

s3_client = boto3.client(
    "s3",
    aws_access_key_id=AWS_ACCESS_KEY,
    aws_secret_access_key=AWS_SECRET_KEY,
    region_name=AWS_REGION_S3
)

# Pydantic model for input request
class UserApplicationRequest(BaseModel):
    application_type_id: int  # This is council_application_map.id
    application_name: str
    site_post_code: str
    site_address: str

class UserData(BaseModel):
    field_id: int
    field_name: str
    field_value: str

class HabitatMetricsInput(BaseModel):
    application_id: int
    row_id: int
    habitat_type: str
    broad_habitat_group: str
    strategic_significance: str
    total_area: float
    area_retained: float
    area_enhanced: float

# Pydantic model for request body
class UserApplicationRequest(BaseModel):
    user_application_id: int
    # field_ids: list[int]

class CommentsRequest(BaseModel):
    user_application_id: int
    comment: str

class FetchStepFieldsRequest(BaseModel):
    user_application_id: int
    section_ids: List[int]

class EvaluateApplicationRequest(BaseModel):
    user_application_id: int
    flag: bool

class MappedResponseRequest(BaseModel):
    user_application_id: int
    section_ids: List[int]

class CommentRequest(BaseModel):
    user_application_id: int
    flag: bool
    comments: Optional[str] = None
class PDFGenerationResponse(BaseModel):
    file_key: str

# Function to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_field_mapping(db: Session):
    fields = db.query(FieldMaster).all()
    return {field.id: field.name for field in fields}


def get_application_mapping(db: Session):
    apps = db.query(UserApplicationDetails).all()
    return {app.id: app.application_name for app in apps}


def get_section_mapping(db: Session):
    sections = db.query(SectionMaster).all()
    return {str(section.id): section.name for section in sections}


# Bedrock client
AWS_REGION = os.getenv("AWS_REGION")
AWS_ACCESS_KEY = os.getenv('AWS_ACCESS_KEY')
AWS_SECRET_KEY = os.getenv('AWS_SECRET_KEY')
MODEL_ID = os.getenv("MODEL_ID")

# ---- Initialize Bedrock client & embeddings ----
bedrock_client = boto3.client(service_name="bedrock-runtime",region_name=AWS_REGION,aws_access_key_id=os.getenv("AWS_ACCESS_KEY"),
    aws_secret_access_key=os.getenv("AWS_SECRET_KEY"))

def send_application_status_email(email: str, user_role: str, is_approved: bool, comments: Optional[str] = None):
    """
    Send an email to the user about their application status.
    
    Args:
        email (str): Recipient's email address
        user_role (str): Role of the person who validated/approved (e.g., 'admin', 'planner')
        is_approved (bool): Whether the application was approved or rejected
        comments (Optional[str]): Comments/suggestions if the application was rejected
    """
    subject = "Application Status Update"
    
    # Determine the status message based on role and approval
    if user_role == "admin":
        if is_approved:
            status_message = "Your application has been validated by the admin."
            body = f"""
            <html>
                <body>
                    <h1>Application Validated</h1>
                    <p>{status_message}</p>
                    <p>Your application has passed the initial validation and will now be reviewed by a planner.</p>
                </body>
            </html>
            """
        else:
            status_message = "Your application has been invalidated by the admin and requires attention."
            body = f"""
            <html>
                <body>
                    <h1>Application Requires Attention</h1>
                    <p>{status_message}</p>
                    <p>Please review the following comments and make necessary changes:</p>
                    <div style="background-color: #f5f5f5; padding: 15px; border-left: 4px solid #ff0000;">
                        <p>{comments if comments else "No specific comments provided."}</p>
                    </div>
                    <p>Please make the required changes and resubmit your application.</p>
                </body>
            </html>
            """
    else:  # planner role
        if is_approved:
            status_message = "Your application has been approved by the planner."
            body = f"""
            <html>
                <body>
                    <h1>Application Approved</h1>
                    <p>{status_message}</p>
                    <p>Your application has been fully approved. You can now proceed with the next steps in your planning process.</p>
                </body>
            </html>
            """
        else:
            status_message = "Your application has been refused by the planner and requires attention."
            body = f"""
            <html>
                <body>
                    <h1>Application Refused</h1>
                    <p>{status_message}</p>
                    <p>Please review the following comments and make necessary changes:</p>
                    <div style="background-color: #f5f5f5; padding: 15px; border-left: 4px solid #ff0000;">
                        <p>{comments if comments else "No specific comments provided."}</p>
                    </div>
                    <p>Please make the required changes and resubmit your application.</p>
                </body>
            </html>
            """
    
    try:
        SMTP_CLIENT.send_email(
            Source=SMTP_SENDER_EMAIL,
            Destination={"ToAddresses": [email]},
            Message={
                "Subject": {"Data": subject},
                "Body": {"Html": {"Data": body}}
            },
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Email sending failed: {e}")

def verify_token(authorization: Optional[str], db: Session):
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Unauthorized")
    access_token = authorization.split("Bearer ")[1]
    session_exists = db.query(UserSessions).filter(UserSessions.access_token == access_token).first()
    if not session_exists:
        raise HTTPException(status_code=403, detail="Invalid access token")
    return session_exists.user_id

# Function to get user from access token (using user_sessions table)
def get_user_from_token(access_token: str, db: Session):
    session_entry = db.query(UserSessions).filter(UserSessions.access_token == access_token).first()
    return session_entry.user_id if session_entry else None

@app.put("/create-user-applications", response_model=dict)
def create_user_application(
    request: UserApplicationRequest,
    authorization: Optional[str] = Header(None),
    db: Session = Depends(get_db)
):
    user_id=verify_token(authorization, db)

    # Fetch application details using ORM
    mapping_entry = db.query(CouncilApplicationMap).filter_by(id=request.application_type_id).first()
    if not mapping_entry:
        raise HTTPException(status_code=404, detail="Invalid application_type_id")

    application_type = db.query(ApplicationTypes).filter_by(id=mapping_entry.application_id).first()

    # Insert new record using ORM
    new_user_application = UserApplicationDetails(
        user_id=user_id,
        application_id=mapping_entry.application_id,
        stature=1,  # Default Stature
        application_name=request.application_name,
        site_post_code=request.site_post_code,
        site_address=request.site_address
    )
    db.add(new_user_application)
    db.commit()
    db.refresh(new_user_application)

    return {"user_application_id": new_user_application.id}

@app.get("/get-user-applications", response_model=list)
def get_user_applications(
    authorization: str = Header(None),
    db: Session = Depends(get_db)
):
    user_id = verify_token(authorization, db)

    # Fetch user applications from user_application_details table
    applications = db.query(UserApplicationDetails).filter(UserApplicationDetails.user_id == user_id).all()

    if not applications:
        return []
    result = []
    for app in applications:
        status = db.query(UserApplicationStatus).filter(UserApplicationStatus.id == app.stature).first()
        result.append({
            "application_id": app.id,
            "application_name": app.application_name,
            "site_post_code": app.site_post_code,
            "site_address": app.site_address,
            "stature": status.applicant_status if status else None
        })
    return result


@app.post("/fetch_step_fields")
def fetch_step_fields(request: FetchStepFieldsRequest, authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    verify_token(authorization, db)
    
    response_data = {}
    
    # Fetch data for each section
    for section_id in request.section_ids:
        field_ids = [field.id for field in db.query(FieldMaster.id).filter(FieldMaster.section == section_id).all()]
        rows = db.query(UserInputs).filter(UserInputs.user_application_id == request.user_application_id, UserInputs.field.in_(field_ids)).all()
        
        if rows:
            response_data[str(section_id)] = {"data": rows}
        else:
            response_data[str(section_id)] = {"data": []}
    
    # Check for pre-flight data
    pre_flight_record = db.query(PreFlightMaster).filter(
        PreFlightMaster.user_application_id == request.user_application_id
    ).first()
    
    if pre_flight_record:
        response_data["pre-flight"] = {
            "score": pre_flight_record.pre_flight_score,
            "suggested_edits": pre_flight_record.suggested_edits
        }
    
    if not response_data:
        raise HTTPException(status_code=404, detail="No data found")
        
    return response_data

@app.post("/mapped_response")
def mapped_response(request: FetchStepFieldsRequest, authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    try:
        # Get raw response from internal function
        raw_response = fetch_step_fields(request, authorization, db)
        
        # Load mapping data
        field_map = get_field_mapping(db)
        app_map = get_application_mapping(db)
        section_map = get_section_mapping(db)

        # Prepare mapped output
        mapped_output = {}
        
        for section_id, section_content in raw_response.items():
            if section_id == "pre-flight":
                suggested_edits = section_content.get("suggested_edits", [])
                edits_mapped = []
                
                for edit in suggested_edits:
                    field_name = edit.get("field_name") or field_map.get(edit.get("field_id"), f"[Field {edit.get('field_id')}]")
                    edits_mapped.append({
                        "field_name": field_name,
                        "user_input": edit.get("user_input"),
                        "suggestion": edit.get("suggestion")
                    })
                mapped_output["pre-flight"] = {"suggested_edits": edits_mapped}
                continue

            section_name = section_map.get(section_id, f"[Section {section_id}]")
            entries = []
            
            for entry in section_content.get("data", []):
                field_id = entry.field
                app_id = entry.user_application_id
                entries.append({
                    "field_name": field_map.get(field_id, f"[Field {field_id}]"),
                    "application_name": app_map.get(app_id, f"[Application {app_id}]"),
                    "user_input": entry.user_input
                })
            mapped_output[section_name] = entries

        return mapped_output

    except HTTPException as http_exc:
        raise http_exc  # Let FastAPI handle already-raised HTTP errors

    except Exception as e:
        # Catch unexpected errors
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

@app.post("/update_step_fields/{user_application_id}/")
def update_step_fields(user_application_id: int, data: UserData, authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    verify_token(authorization, db)

    # Check if the user_application_id and field_id already exist
    existing_record = db.query(UserInputs).filter(
        UserInputs.user_application_id == user_application_id,
        UserInputs.field == data.field_id
    ).first()

    if existing_record:
        # If record exists, update it
        existing_record.user_input = data.field_value
        db.commit()
    else:
        # Insert a new record
        new_user_input = UserInputs(
            user_application_id=user_application_id,
            field=data.field_id,
            user_input=data.field_value,
            validation_score=getattr(data, 'validation_score', 0.0)
        )

        db.add(new_user_input)  # Add new record
        db.commit()  # Save changes
        db.refresh(new_user_input)  # Refresh instance to get the ID

    # ✅ Update pre_flight_review.is_updated to True if exists
    pre_flight_record = db.query(PreFlightMaster).filter(
        PreFlightMaster.user_application_id == user_application_id
    ).first()

    if pre_flight_record and not pre_flight_record.is_updated:
        pre_flight_record.is_updated = True
        db.commit()
    
    return {"message": "Fields updated successfully."}

@app.post("/upload_documents")
async def upload_documents(
    files: List[UploadFile] = File(description="PDF files to upload"),
    authorization: Optional[str] = Header(None),
    db: Session = Depends(get_db)
):
    """
    Upload multiple PDF files.
    The files should be sent as form-data with the key 'files'.
    """
    verify_token(authorization, db)
    
    if not files:
        raise HTTPException(status_code=400, detail="No files provided")
    
    file_keys = []
    for file in files:
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail=f"File {file.filename} is not a PDF")
            
        file_key = f"uploads/{uuid.uuid4()}_{file.filename}"
        try:
            file_content = await file.read()
            s3_client.put_object(
                Bucket=S3_BUCKET_NAME,
                Key=file_key,
                Body=file_content,
                ContentType='application/pdf',
                ACL="public-read"
            )
            file_keys.append({"file_key": file_key})
        except ClientError as e:
            raise HTTPException(status_code=500, detail=str(e))
        finally:
            await file.close()
    
    return file_keys

@app.delete("/delete_documents")
def delete_documents(file_key: str, authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    verify_token(authorization, db)
    try:
        s3_client.delete_object(Bucket=S3_BUCKET_NAME, Key=file_key)
        return {"message": "File deleted successfully."}
    except ClientError as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/fetch_documents")
def fetch_documents(file_key: str, authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    verify_token(authorization, db)
    try:
        url = s3_client.generate_presigned_url(
            ClientMethod='get_object',
            Params={
                'Bucket': S3_BUCKET_NAME,
                'Key': file_key,
                'ResponseContentType': 'application/pdf',
                'ResponseContentDisposition': 'inline'
            },
            ExpiresIn=3600  # 1 hour expiration
        )
        return {"presigned_url": url}
    except ClientError as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/pre-flight")
async def validate_fields(request: UserApplicationRequest, authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):

    # Verify the tokens
    user_id = verify_token(authorization, db)
    # Check the user_application_id for the user is valid or not
    user_app = db.query(UserApplicationDetails).filter_by(
        id=request.user_application_id,
        user_id=user_id
    ).first()
    if not user_app:
        raise HTTPException(status_code=403, detail="Unauthorized access to the application.")

    # Check if a pre-flight record already exists
    existing_review = db.query(PreFlightMaster).filter(
        PreFlightMaster.user_application_id == request.user_application_id
    ).first()

    # If exists and is_updated == False, return stored results
    if existing_review and not existing_review.is_updated:
        return {
            "overall_score": existing_review.pre_flight_score.get("overall_score"),
            **{f"pre_flight_score_type_{t}": v for t, v in existing_review.pre_flight_score.items() if t != "overall_score"},
            "incorrect_fields": existing_review.suggested_edits or []
        }
    
    # --- NEW: fetch *all* requested fields ---
    all_fields = (
        db.query(FieldMaster)
            .join(ApplicationTypes, FieldMaster.application_type == ApplicationTypes.id)
            .join(CouncilApplicationMap, ApplicationTypes.id == CouncilApplicationMap.application_id)
            .join(UserApplicationDetails, CouncilApplicationMap.id == UserApplicationDetails.application_id)
            .filter(UserApplicationDetails.id == request.user_application_id)
            .order_by(FieldMaster.type, FieldMaster.id)  # Optional: for consistent ordering
            .all()
    )

    user_inputs = (
        db.query(UserInputs)
          .filter(UserInputs.user_application_id == request.user_application_id)
          .all()
    )
    user_input_map = {ui.field: ui for ui in user_inputs}

    fields_to_validate = []
    for field in all_fields:
        if field.is_optional == 0:  # Always include mandatory fields
            fields_to_validate.append(field)
        elif field.is_optional == 1 and field.id in user_input_map:  # Include optional fields only if they have input
            fields_to_validate.append(field)

    if not fields_to_validate:
        raise HTTPException(
            status_code=404,
            detail="No valid fields found for validation"
        )

    # 2. Group inputs by type
    grouped_by_type: dict[int, list[dict]] = {}
    for field in fields_to_validate:
        user_input = user_input_map.get(field.id)
        if user_input:
            grouped_by_type.setdefault(field.type, []).append({
                "field_id": field.id,
                "field_name": field.name,
                "user_input": user_input.user_input,
                "field_weight": field.weightage
            })
    
    # 3. Prepare containers
    incorrect_fields: list[dict] = []
    validation_map: dict[int, int] = {}
    pre_flight_scores: dict[int, float] = {}
    # DEBUG container—remove this block later
    calculations: dict = {
        "type_calculations": {},
        "overall_calculation": {}
    }

    document_inputs = []
    if 3 in grouped_by_type:
        for entry in grouped_by_type[3]:
            if entry["field_id"] == 165:
                try:
                    # Handle the file path from the database
                    file_path = entry["user_input"]
                    if not file_path:
                        continue  # Skip if no file path provided
                        
                    if isinstance(file_path, str):
                        try:
                            # Parse the JSON string if it's stored as a string
                            file_path = json.loads(file_path)
                        except json.JSONDecodeError:
                            # If not JSON, use as is
                            pass
                            
                    # Get all file paths from the dictionary
                    if isinstance(file_path, dict):
                        if not file_path:  # Check if dictionary is empty
                            continue
                        # Add all values from the dictionary instead of just the first one
                        document_inputs.extend(file_path.values())
                    else:
                        # If it's a single file path (string), add it directly
                        document_inputs.append(file_path)
                except Exception as e:
                    # Log the error but continue processing other entries
                    print(f"Error processing file path for field 165: {str(e)}")
                    continue
    # If no valid document inputs found, return early with appropriate message
    if not document_inputs:
        field_165 = next((f for f in all_fields if f.id == 165), None)
        return {
            "overall_score": 0,
            "pre_flight_score_type_3": 0,
            "incorrect_fields": [{
                "section_id": field_165.section if field_165 else None,
                "field_id": 165,
                "field_name": field_165.name if field_165 else "Document Upload",
                "user_input": "No documents found",
                "suggestion": "Please upload at least one valid document"
            }],
            "calculations": {
                "type_calculations": {},
                "overall_calculation": {
                    "overall_weighted_sum": 0,
                    "overall_total_weight": 0,
                    "overall_score": 0
                }
            }
        }
 
    document_request = DocumentValidationRequest(file_keys=document_inputs)
    validation_result = await validate_document(document_request, authorization, db)
 
    # Collect checklist and final_score for each document
    document_checklists = validation_result["checklist"]
    document_final_scores = validation_result["final_score"]

    # Calculate validation score for field 165 using final_score from /validate_document
    if document_checklists:
        field_165 = next((f for f in all_fields if f.id == 165), None)
        if field_165:
 
            # Add to validation_map for pre-flight score calculation
            validation_map[165] = document_final_scores
 
            # Update the validation score in UserInputs table
            user_input_record = db.query(UserInputs).filter(
                UserInputs.user_application_id == request.user_application_id,
                UserInputs.field == 165
            ).first()
 
            if user_input_record:
                user_input_record.validation_score = document_final_scores
                db.commit()
           
            # Get field 165 details from database
            field_165_details = db.query(FieldMaster).filter(FieldMaster.id == 165).first()
 
            # Add document checklist suggestions to incorrect_fields
            if field_165_details:
                document_suggestions = {}
                document_suggestions["segmentation"] = document_checklists.get("segmentation", {}).get("edit", [])
                for doc in document_checklists.get("ocr_orientation", []):
                    document_suggestions[doc["file_key"]] = doc.get("edit", [])
                incorrect_fields.append({
                    "section_id": field_165_details.section,
                    "field_id": 165,
                    "field_name": field_165_details.name,
                    "user_input": document_inputs,
                    "suggestion": document_suggestions
                })
    MAX_ENTRIES_PER_CHUNK = 20  # Tune this based on token limits and entry length

    # 4. Validate each group via LLM with chunking
    for t_id, entries in grouped_by_type.items():
        num_chunks = ceil(len(entries) / MAX_ENTRIES_PER_CHUNK)

        chunked_results = []

        # 4. Validate each group via LLM
        for i in range(num_chunks):

            chunk_entries = entries[i * MAX_ENTRIES_PER_CHUNK : (i + 1) * MAX_ENTRIES_PER_CHUNK]
            chunk_entries = [entry for entry in chunk_entries if entry["field_id"] != 165]
            # 1) Build your system + user messages
            system_prompt = (
                "You are validating form fields. Each entry has a `field_name` and a `user_input`.\n"
                "- If valid, score 1.\n"
                "- If invalid, score 0 and suggest a correction.\n"
                "- For JSON formatted inputs (like {\"Key\":\"Value\"}), validate each key-value pair against the field name.\n"
                "- For example, if field_name is 'Title number(s)' and user_input is {\"Key\":\"Value\"}, validate that 'Value' is a valid title number.\n"
                "- Return validation score 1 only if all key-value pairs in the JSON are valid.\n"
                "- If any key-value pair is invalid, return score 0 and explain which pair(s) are invalid."
                "- When providing suggestions, always include examples of correct values for that field.\n"
                "- For example, if a date is invalid, suggest the correct format like 'DD-MM-YYYY' and provide an example like '20-03-2024'.\n"
                "- For JSON inputs, provide examples of valid key-value pairs that would be acceptable."
            )

            def format_user_input(user_input):
                if isinstance(user_input, str) and user_input.strip().startswith("{"):
                    return user_input  # Return JSON as is
                return f'"{user_input}"'  # Wrap non-JSON in quotes

            user_entries = "\n".join(
                f'{i+1}. field_id: {e["field_id"]}, '
                f'field_name: "{e["field_name"]}", '
                f'user_input: {format_user_input(e["user_input"])}'
                for i, e in enumerate(chunk_entries)
            )

            user_prompt = (
                f"Entries to validate:\n{user_entries}\n\n"
                "IMPORTANT: Respond *only* with a JSON object in this format:\n"
                '{ "results": [ { "field_id": <num>, '
                '"validation_score": 1 or 0, "suggestion": "<string>" }, ... ] }'
            )

            # 2) Call the Converse API correctly
            resp = bedrock_client.converse(
                modelId=MODEL_ID,                             # your model
                system=[{"text": system_prompt}],             # system instruction
                messages=[{"role": "user", "content": [{"text": user_prompt}]}],
                inferenceConfig={                             # inference params
                    "maxTokens":   2048,
                    "temperature": 0.2,
                    "topP":        0.9
                }
            )

            # 3) Extract the assistant's reply
            # with bedrock-runtime, resp is already a dict
            # Bedrock chat models return something like:
            # { "output":[{ "message":{ "content":"<the text>" }, ... }], ... }
            try:
                model_output = resp["output"]["message"]["content"][0]["text"]
            except (KeyError, IndexError):
                raise HTTPException(
                    status_code=502,
                    detail=f"Unexpected LLM response shape for type {t_id}: {resp}"
                )
            # DEBUG: print or log the raw output so you can inspect what the LLM actually returned
            # print(f"[DEBUG] Raw LLM output for type {t_id}:\n{model_output}\n")

            # Extract JSON substring
            raw = model_output.strip()
            # print(f"raw == {raw}")
            # If they wrapped it in triple backticks, remove them
            json_block = raw
            if raw.startswith("```"):
                # drop the first and last lines of backticks
                lines = raw.splitlines()
                # print(lines)
                # find the line with ```json
                start_idx = next((i for i,l in enumerate(lines) if l.strip().startswith("```json")), None)
                # find the last ``` 
                end_idx = next((i for i,l in enumerate(lines) if l.strip() == "```" and i>start_idx), None)
                if start_idx is None or end_idx is None:
                    raise HTTPException(
                        status_code=502,
                        detail=(
                            f"Could not locate JSON fences in LLM output for type {t_id}:\n"
                            f"{model_output!r}"
                        )
                    )
                json_block = "\n".join(lines[start_idx+1 : end_idx])


            # find the JSON braces
            o = json_block.find("{")
            c = json_block.rfind("}")
            if o == -1 or c == -1 or o > c:
                raise HTTPException(
                    status_code=502,
                    detail=(
                        f"LLM output did not contain a valid JSON object for type {t_id}.\n"
                        f"Here's what we got:\n{json_block[:300]!r}"
                    )
                )
            json_str = json_block[o : c+1]

            # Parse it
            try:
                result_data = json.loads(json_str)
            except json.JSONDecodeError as e:
                raise HTTPException(
                    status_code=502,
                    detail=(
                        f"JSON parse error: {e.msg}\n"
                        f"JSON snippet:\n{json_str}"
                    )
                )
            chunked_results.extend(result_data.get("results", []))
        

        # 5. Process LLM results
        for r in chunked_results:
            fid = r.get("field_id")
            score = 1 if r.get("validation_score") == 1 else 0
            validation_map[fid] = score

            # Update DB record
            user_input = user_input_map.get(fid)
            if user_input:
                user_input.validation_score = float(score)

            if score == 0:
                fld = next(f for f in fields_to_validate if f.id == fid)
                incorrect_fields.append({
                    "section_id": fld.section,
                    "field_id": fid,
                    "field_name": fld.name,
                    "user_input": user_input.user_input if user_input else "",
                    "suggestion": r.get("suggestion", "")
                })

        # After you load field_records from FieldMaster.filter(id.in_(request.field_ids)):
        type_total_weight: dict[int, float] = {}
        for f in fields_to_validate:
            type_total_weight.setdefault(f.type, 0.0)
            type_total_weight[f.type] += f.weightage

        # 6. Compute this type's pre-flight score
        for t_id, entries in grouped_by_type.items(): # Added loop to include every field in the calculation for single type.
            # total_w = sum(e["field_weight"] for e in entries) # Only for the fields present in the user_inputs table.
            total_w = type_total_weight.get(t_id, 0.0) # For all the fields present in the fields table for one type.
            weighted_sum = sum(
                e["field_weight"] * validation_map.get(e["field_id"], 0)
                for e in entries
            )
            score = (weighted_sum / total_w) if total_w else 0.0
            pre_flight_scores[t_id] = score
            # TEMP: record the raw numbers
            calculations["type_calculations"][t_id] = {
                "weighted_sum":       weighted_sum,
                "total_field_weight": total_w,
                "pre_flight_score":   score
            }


    # 7. Compute overall score
    type_records = db.query(TypeMaster).filter(TypeMaster.id.in_(pre_flight_scores.keys())).all()
    type_weight_map = {t.id: t.weightage for t in type_records}
    num = sum(type_weight_map[t] * pre_flight_scores[t] for t in pre_flight_scores)
    den = sum(type_weight_map[t] for t in pre_flight_scores)
    overall_score = round((num / den)*100) if den else 0.0

    # TEMP: record the overall calculation
    calculations["overall_calculation"] = {
        "overall_weighted_sum": num,
        "overall_total_weight": den,
        "overall_score": overall_score
    }

    # 7) Build response
    response = {
        "overall_score": overall_score,
        **{f"pre_flight_score_type_{t}": s for t, s in pre_flight_scores.items()},
        "incorrect_fields": incorrect_fields,
        "calculations":calculations
    }
    
    # Insert or update pre_flight_review table
    pre_flight_score_json = {
        "overall_score": overall_score,
        **{f"{t}": s for t, s in pre_flight_scores.items()}
    }

    # Insert new record if not present
    if not existing_review:
        review_entry = PreFlightMaster(
            user_application_id=request.user_application_id,
            pre_flight_score=pre_flight_score_json,
            suggested_edits=incorrect_fields,
            is_updated=False
        )
        db.add(review_entry)
    else:
        # Update the existing record if is_updated is True
        existing_review.pre_flight_score = pre_flight_score_json
        existing_review.suggested_edits = incorrect_fields
        existing_review.is_updated = False  # Reset status
    db.commit()

    return response

@app.post("/comments")
def add_comments(request: CommentsRequest, authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    verify_token(authorization, db)

    user_application = db.query(PreFlightMaster).filter_by(user_application_id=request.user_application_id).first()
    if not user_application:
        raise HTTPException(status_code=404, detail="User application not found")

    user_application.comments = request.comment
    db.commit()
    return {"message": "Comment added successfully"}

@app.post("/validate_document")
async def validate_document(request: DocumentValidationRequest, authorization: Optional[str] = Header(None), db=Depends(get_db)):
    verify_token(authorization, db)
    result = validate_multiple(request.file_keys)
    if result.get("status") == "error":
        from utils.segmentation import CHECK
        return {
            "ocr_orientation_score": 0,
            "segmentation_score": 0,
            "final_score": 0,
            "checklist": {
                "ocr_orientation": [],
                "segmentation": {"score": 0, "edit": ["Validation error: " + result.get("message", "Unknown error")]}
            }
        }

    # --- OCR & Orientation ---
    individual_validations = result.get("individual_validations", [])
    ocr_orientation_list = []
    ocr_scores = []
    for file in individual_validations:
        score = file.get("score", 0)
        file_key = file.get("file_key")
    
        ocr_orientation_suggestions = file.get("checklist", {}).get("suggestions", [])
        ocr_orientation_list.append({
            "file_key": file_key,
            "score": score,
            "edit": ocr_orientation_suggestions
        })
        ocr_scores.append(score)

    max_ocr_score = 11
    num_files = len(ocr_scores)
    ocr_orientation_score = (sum(ocr_scores) / (num_files * max_ocr_score)) if num_files else 0

    # --- Segmentation ---
    collective_segmentation = result.get("collective_segmentation", {})
    seg_report = collective_segmentation.get("validation_report", {})
    # count "FOUND" as 1, "MISSING" as 0, sum for score
    seg_checklist = seg_report.get("checklist_validation", {})
    seg_score_raw = sum(3 for v in seg_checklist.values() if "FOUND" in v)
    max_seg_score = 18
    segmentation_score = seg_score_raw / max_seg_score if max_seg_score else 0

    final_score = (ocr_orientation_score + segmentation_score) / 2

    checklist = {
        "ocr_orientation": ocr_orientation_list,
        "segmentation": {
            "score": seg_score_raw,
            "edit": [k for k, v in seg_checklist.items() if "MISSING" in v]
        }
    }

    output = {
        "ocr_orientation_score": round(ocr_orientation_score, 3),
        "segmentation_score": round(segmentation_score, 3),
        "final_score": round(final_score, 3),
        "checklist": checklist
    }
    return output

@app.post("/preview_mode", response_model=PDFGenerationResponse)
def preview_mode(
    request: MappedResponseRequest,
    authorization: Optional[str] = Header(None),
    db: Session = Depends(get_db)
):
    verify_token(authorization, db)
    temp_dir = tempfile.mkdtemp()
    attached_pdfs = []

    def draw_disclaimer(canvas, x, y, max_width):
        text = "*This is a reference copy of the application. Not valid for submission to Local Planning Authority"
        canvas.setFillColor(colors.red)
        canvas.setFont("Helvetica", 8)
        words = text.split()
        lines, line = [], ""
        for word in words:
            test_line = f"{line} {word}".strip()
            if canvas.stringWidth(test_line, "Helvetica", 8) <= max_width:
                line = test_line
            else:
                lines.append(line)
                line = word
        if line:
            lines.append(line)
        for i, line in enumerate(lines):
            canvas.drawRightString(x, y - (i * 10), line)

    def draw_first_page(canvas, doc):
        canvas.setFillColor(colors.HexColor("#3EB489"))
        canvas.setFont("Helvetica-Bold", 16)
        canvas.drawString(30, A4[1] - 30, "Paive")
        now = datetime.now().strftime("%d %B %Y, %I:%M %p")
        canvas.setFont("Helvetica", 10)
        canvas.setFillColor(colors.black)
        canvas.drawString(30, A4[1] - 50, f"{now}")
        draw_disclaimer(canvas, A4[0] - 30, A4[1] - 30, max_width=180)
        canvas.setFillColor(colors.HexColor("#3EB489"))
        canvas.rect(0, 0, A4[0], 10, fill=1, stroke=0)

    def draw_other_pages(canvas, doc):
        draw_disclaimer(canvas, A4[0] - 30, A4[1] - 30, max_width=180)
        canvas.setFillColor(colors.HexColor("#3EB489"))
        canvas.rect(0, 0, A4[0], 10, fill=1, stroke=0)

    def get_presigned_url(file_key: str) -> Optional[str]:
        try:
            return s3_client.generate_presigned_url(
                ClientMethod='get_object',
                Params={'Bucket': S3_BUCKET_NAME, 'Key': file_key},
                ExpiresIn=3600
            )
        except ClientError:
            return None

    try:
        # Fetch pre-flight record for this application ID only
        pre_flight = db.query(PreFlightMaster).filter(
            PreFlightMaster.user_application_id == request.user_application_id
        ).first()

        if not pre_flight:
            raise HTTPException(status_code=404, detail="Pre-flight entry not found")

        existing_file_key = getattr(pre_flight, 'preview_doc', None)
        is_update_flag = pre_flight.is_updated

        # Block execution if preview_doc exists and is_updated is False
        if existing_file_key and not is_update_flag:
            return {"file_key": existing_file_key}
        
        # Delete existing file from S3 if applicable
        if existing_file_key:
            try:
                s3_client.delete_object(Bucket=S3_BUCKET_NAME, Key=existing_file_key)
            except ClientError as e:
                raise HTTPException(status_code=500, detail=f"Failed to delete previous preview: {str(e)}")

        # Proceed with generating the new PDF
        data = mapped_response(request, authorization, db)
        styles = getSampleStyleSheet()
        style_normal = styles["Normal"]
        style_heading = styles["Heading2"]
        elements = []

        doc_sections = {"Upload Documents", "Plans and Drawings", "Location Plan"}
        main_pdf_path = os.path.join(temp_dir, "main.pdf")
        final_pdf_path = os.path.join(temp_dir, "final.pdf")

        doc = SimpleDocTemplate(main_pdf_path, pagesize=A4, rightMargin=30, leftMargin=30, topMargin=30, bottomMargin=30)
        elements.append(Spacer(1, 60))
        application_name = next((section[0].get("application_name", "Application Summary")
                                 for section in data.values() if isinstance(section, list) and section), "Application Summary")
        elements.append(Paragraph(application_name, styles["Title"]))
        elements.append(Spacer(1, 40))

        for section_title, section_data in data.items():
            if section_title == "pre-flight":
                continue

            elements.append(Spacer(1, 24))
            elements.append(Paragraph(f"<b>{section_title}</b>", style_heading))
            table_data = []
            is_doc_section = section_title.strip().lower() in {s.lower() for s in doc_sections}

            for item in section_data:
                field = item.get("field_name", "")
                value = item.get("user_input", "")

                if is_doc_section and isinstance(value, str):
                    try:
                        file_dict = json.loads(value)
                        if not isinstance(file_dict, dict):
                            raise ValueError("Expected dict of files")

                        file_links = []
                        for filename, path in file_dict.items():
                            cleaned_path = path.split("uploads/")[-1]
                            file_key = f"uploads/{cleaned_path}"
                            url = get_presigned_url(file_key)

                            if url:
                                response = requests.get(url)
                                if response.status_code == 200:
                                    safe_field = field.replace(" ", "_").lower()
                                    pdf_filename = f"{safe_field}_{filename}".replace(" ", "_")
                                    pdf_path = os.path.join(temp_dir, pdf_filename)
                                    with open(pdf_path, "wb") as f:
                                        f.write(response.content)
                                    attached_pdfs.append((field, filename, pdf_path))
                                    file_links.append(filename)
                                else:
                                    file_links.append(f"{filename} (Download failed)")
                            else:
                                file_links.append(f"{filename} (Invalid URL)")

                        value_display = "<br/>".join(file_links)
                        table_data.append([Paragraph(field, style_normal), Paragraph(value_display, style_normal)])
                    except Exception as e:
                        table_data.append([Paragraph(field, style_normal), Paragraph(f"Error processing documents: {str(e)}", style_normal)])
                else:
                    try:
                        parsed = json.loads(value)
                        if isinstance(parsed, dict):
                            for sub_key, sub_val in parsed.items():
                                label = f"{field} - {sub_key}" if field else sub_key
                                table_data.append([Paragraph(label, style_normal), Paragraph(str(sub_val), style_normal)])
                        else:
                            table_data.append([Paragraph(field, style_normal), Paragraph(str(parsed), style_normal)])
                    except Exception:
                        table_data.append([Paragraph(field, style_normal), Paragraph(str(value), style_normal)])

            table = Table(table_data, colWidths=[180, 330])
            table.setStyle(TableStyle([
                ('BOX', (0, 0), (-1, -1), 1, colors.black),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ]))
            elements.append(KeepTogether([Spacer(1, 6), table]))
            elements.append(Spacer(1, 12))

        doc.build(elements, onFirstPage=draw_first_page, onLaterPages=draw_other_pages)

        merger = PdfMerger()
        merger.append(main_pdf_path)

        for section_title, filename, pdf_path in attached_pdfs:
            title_page = os.path.join(temp_dir, f"{uuid.uuid4()}.pdf")
            c = canvas.Canvas(title_page, pagesize=A4)
            c.setFont("Helvetica-Bold", 16)
            c.drawString(72, 800, f"{section_title}: {filename}")
            draw_disclaimer(c, A4[0] - 30, A4[1] - 30, max_width=180)
            c.setFillColor(colors.HexColor("#3EB489"))
            c.rect(0, 0, A4[0], 10, fill=1, stroke=0)
            c.save()
            merger.append(title_page)
            merger.append(pdf_path)

        merger.write(final_pdf_path)
        merger.close()

        s3_key = f"generated_pdfs/application_{request.user_application_id}_{datetime.utcnow().strftime('%Y%m%d%H%M%S')}.pdf"
        with open(final_pdf_path, "rb") as f:
            s3_client.upload_fileobj(f, S3_BUCKET_NAME, s3_key, ExtraArgs={"ACL": "public-read"})

        # Update only this application's row
        db.query(PreFlightMaster).filter(
            PreFlightMaster.user_application_id == request.user_application_id
        ).update({"preview_doc": s3_key})
        db.commit()

        return {"file_key": s3_key}

    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)
 
@app.get("/fetch_all_applications")
def fetch_all_applications(authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    user_id = verify_token(authorization, db)
    user_role = db.query(Users.role).filter(Users.id == user_id).scalar()
 
    if not user_role:
        raise HTTPException(status_code=401, detail="Role not found")

    # Base query with join to UserApplicationStatus, CouncilApplicationMap, ApplicationTypes, and CouncilDetails
    base_query = db.query(UserApplicationDetails, UserApplicationStatus, CouncilApplicationMap, ApplicationTypes, CouncilDetails)\
        .join(UserApplicationStatus, UserApplicationDetails.stature == UserApplicationStatus.id)\
        .join(CouncilApplicationMap, UserApplicationDetails.application_id == CouncilApplicationMap.id)\
        .join(ApplicationTypes, CouncilApplicationMap.application_id == ApplicationTypes.id)\
        .join(CouncilDetails, CouncilApplicationMap.council_id == CouncilDetails.council_id)
    
    if user_role == "admin": #admin
        applications = base_query.filter(UserApplicationDetails.stature.notin_((1,2,3))).all()
        return {
            "applications": [{
                "id": app.UserApplicationDetails.id,
                "application_name": app.UserApplicationDetails.application_name,
                "site_address": app.UserApplicationDetails.site_address,
                "site_post_code": app.UserApplicationDetails.site_post_code,
                "status_id": app.UserApplicationDetails.stature,
                "status": app.UserApplicationStatus.admin_status,
                "application_type": app.ApplicationTypes.kind,
                "council": app.CouncilDetails.council_name,
                "validation_timestamp": app.UserApplicationDetails.validation_timestamp,
                "approval_timestamp": app.UserApplicationDetails.approval_timestamp
            } for app in applications]
        }
    elif user_role == "planner": #planner
        applications = base_query.filter(UserApplicationDetails.stature.notin_((1,2,3,4,6))).all()
        return {
            "applications": [{
                "id": app.UserApplicationDetails.id,
                "application_name": app.UserApplicationDetails.application_name,
                "site_address": app.UserApplicationDetails.site_address,
                "site_post_code": app.UserApplicationDetails.site_post_code,
                "status_id": app.UserApplicationDetails.stature,
                "status": app.UserApplicationStatus.planner_status,
                "application_type": app.ApplicationTypes.kind,
                "council": app.CouncilDetails.council_name,
                "approval_timestamp": app.UserApplicationDetails.approval_timestamp
            } for app in applications]
        }
    else:
        return {"you are not authorized to fetch all applications"}

@app.put("/submit_application")
def submit_application(request: UserApplicationRequest, authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    user_id = verify_token(authorization, db)

    user_app = db.query(UserApplicationDetails).filter_by(
        id=request.user_application_id,
        user_id=user_id
    ).first()
    if not user_app:
        raise HTTPException(status_code=403, detail="Unauthorized access to the application.")
    else:
        user_app.stature = 4
        user_app.validation_timestamp = datetime.now(UTC)
        db.commit()

@app.post("/evaluate_application")
def council_comments(request: CommentRequest, authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    user_id = verify_token(authorization, db)
    user_role = db.query(Users.role).filter(Users.id == user_id).scalar()
 
    if not user_role:
        raise HTTPException(status_code=401, detail="Role not found")
    
    # Check if the user_application_id and field_id already exist
    existing_record = db.query(CouncilComments).filter(
        CouncilComments.user_application_id == request.user_application_id,
        CouncilComments.user_id == user_id
    ).first()

    application = db.query(UserApplicationDetails).filter(
        UserApplicationDetails.id == request.user_application_id
    ).first()
    if not application:
        raise HTTPException(status_code=404, detail="Application not found")
    
    user_email = db.query(Users.email).filter(Users.id == application.user_id).scalar()
    if not user_email:
        raise HTTPException(status_code=404, detail="User email not found")
    
    if user_role == "admin":
        if request.flag:
            send_application_status_email(user_email,user_role,request.flag,None if request.flag else request.comments)
            application.approval_timestamp = datetime.now(UTC)
            application.stature = 5
            db.commit()
            return {"application approved successfully"}
        else:
            if existing_record:
                send_application_status_email(user_email,user_role,request.flag,request.comments)
                existing_record.comments = request.comments
                application.stature = 6
                db.commit()
                return {"comment updated successfully"}
            else:
                send_application_status_email(user_email,user_role,request.flag,request.comments)
                new_comment = CouncilComments(
                    user_application_id=request.user_application_id,
                    user_id=user_id,
                    comments=request.comments)
                application.stature = 6
                db.add(new_comment)
                db.commit()
                return {"comment added successfully"}
            
    elif user_role == "planner":
        if request.flag:
            send_application_status_email(user_email,user_role,request.flag,None if request.flag else request.comments)
            application.stature = 9
            db.commit()
            return {"application approved successfully"}
        else:
            if existing_record:
                send_application_status_email(user_email,user_role,request.flag,request.comments)
                existing_record.comments = request.comments
                application.stature = 10
                db.commit()
                return {"comment updated successfully"}
            else:
                send_application_status_email(user_email,user_role,request.flag,request.comments)
                new_comment = CouncilComments(
                    user_application_id=request.user_application_id,
                    user_id=user_id,
                    comments=request.comments)
                application.stature = 10
                db.add(new_comment)
                db.commit()
                return {"comment added successfully"}
    else:
        return {"you are not authorized to add comments"}