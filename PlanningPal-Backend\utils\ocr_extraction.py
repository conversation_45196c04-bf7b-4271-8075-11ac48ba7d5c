import os
import re
import argparse
from pathlib import Path
import cv2
import numpy as np
from paddleocr import PaddleOCR
from pdf2image import convert_from_path
import matplotlib.pyplot as plt
import fitz
import tempfile
from core.config import s3_client, S3_BUCKET_NAME
from PIL import Image

class ArchitecturalPlanExtractor:
    def __init__(self, language='en', use_gpu=True, debug=False):
        """Initialize the OCR model."""
        self.ocr = PaddleOCR(
            use_angle_cls=True, 
            lang=language, 
            use_gpu=use_gpu,
            det_db_thresh=0.3,
            det_db_box_thresh=0.3,
            det_limit_side_len=2560,  
            rec_batch_num=6,  
            enable_mkldnn=True,  
            cpu_threads=8  # Limit CPU threads
        )
        self.debug = debug
       
        # Enhanced regex patterns for extracting information
        self.scale_pattern = re.compile(r'(?:scale|SC)[:\s]*1[\s:]*(\d+)', re.IGNORECASE)
        self.simple_scale_pattern = re.compile(r'1:(\d+)', re.IGNORECASE)
        self.paper_size_pattern = re.compile(r'\b(A[0-5])\b')
        self.drawing_no_pattern = re.compile(r'(?:DRG\.?(?:\s)?No\.?|drawing\s+number|dwg\.?\s*no\.?)[:\s]*([\w\/\.-]+)', re.IGNORECASE)
       
        # Additional patterns for drawing numbers
        self.additional_drawing_patterns = [
            re.compile(r'\bM\s*\d+\/\d+\/\d+\b'),  # Format: M120/03/1
            re.compile(r'\bPA\s*\d+\/\d+\b'),      # Format: PA00936/011
            re.compile(r'\b[A-Z]\d{1,3}\b'),       # Format: A101
            re.compile(r'\b[A-Z]\d+\/\d+\/\d+\b')  # Format: M115/03/3
        ]
       
    def convert_pdf_to_images(self, pdf_path, dpi=300):
        """Convert PDF pages to images."""
        try:
            return convert_from_path(pdf_path, dpi=dpi)
        except Exception as e:
            print(f"Error converting PDF to images: {e}")
            return []
   
    def preprocess_image(self, image, enhance_contrast=True):
        """Enhanced preprocessing for architectural plans."""
        image_np = np.array(image)

        gray = cv2.cvtColor(image_np, cv2.COLOR_RGB2GRAY)
       
        if enhance_contrast:
            # CLAHE (Contrast Limited Adaptive Histogram Equalization)
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
            gray = clahe.apply(gray)
       
        thresh1 = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
       
        thresh2 = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY, 15, 5
        )
       
        combined = cv2.bitwise_and(thresh1, thresh2)
       
        kernel = np.ones((1, 1), np.uint8)
        cleaned = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel)
       
        return cleaned
   
    def extract_title_block(self, image):
        """Extract and focus on the title block area."""
        width, height = image.size
       
        title_block_region = image.crop((width * 0.7, height * 0.7, width, height))
       
        if self.debug:
            title_block_region.save("debug_title_block.png")
       
        return title_block_region
   
    def extract_text_from_image(self, image, focus_on_title_block=True):
        """Extract all text from an image using PaddleOCR."""
        all_results = []
       
        # Process the entire image
        preprocessed = self.preprocess_image(image)
        full_result = self.ocr.ocr(preprocessed, cls=True)
       
        if full_result and len(full_result) > 0:
            for idx, line in enumerate(full_result):
                if line:
                    for word_info in line:
                        if len(word_info) >= 2:   
                            text = word_info[1][0]  
                            all_results.append(text)
    
        if focus_on_title_block:
            title_block = self.extract_title_block(image)
           
            tb_preprocessed = self.preprocess_image(title_block, enhance_contrast=True)
           
            if self.debug:
                cv2.imwrite("debug_title_block_preprocessed.png", tb_preprocessed)
           
            tb_result = self.ocr.ocr(tb_preprocessed, cls=True)
           
            if tb_result and len(tb_result) > 0:
                for idx, line in enumerate(tb_result):
                    if line:
                        for word_info in line:
                            if len(word_info) >= 2:
                                text = word_info[1][0]
                                all_results.append(text)
       
        return " ".join(all_results)
   
    def find_scale(self, text):
        """Find scale information in the text."""
        match = self.scale_pattern.search(text)
        if match:
            return f"1:{match.group(1)}"
       
        match = self.simple_scale_pattern.search(text)
        if match:
            return f"1:{match.group(1)}"
       
        common_scales = ["1:50", "1:100", "1:200", "1:500", "1:1000", "1:20"]
        for scale in common_scales:
            if scale in text.replace(" ", ""):
                return scale
       
        return None
   
    def find_paper_size(self, text):
        """Find paper size in the text."""
        match = self.paper_size_pattern.search(text)
        if match:
            return match.group(1)
        return None
   
    def find_drawing_number(self, text):
        """Enhanced drawing number detection."""
        if not text:
            return None

        match = self.drawing_no_pattern.search(text)
        if match:
            return match.group(1)

        for pattern in self.additional_drawing_patterns:
            match = pattern.search(text)
            if match:
                return match.group(0)

        specific_formats = [
            (r'M\d+/\d+/\d+', None),  # M120/03/1
            (r'PA\d+/\d+', None),     # PA00936/011
            (r'[A-Z]\d+', None)       # A101
        ]

        for pattern, default in specific_formats:
            match = re.search(pattern, text)
            if match:
                return match.group(0)

        return None
   
    def extract_info_from_pdf(self, pdf_path):
        """Extract scale, paper size, and drawing number from a PDF."""
        try:
            # Convert only the first page
            images = convert_from_path(pdf_path, dpi=300, first_page=1, last_page=1)
            if not images:
                return {"error": "Failed to convert PDF to images"}

            image = images[0]
            results = {
                "scale": None,
                "paper_size": None,
                "drawing_number": None,
                "pdf_path": pdf_path
            }

            # Process both title block and full page
            title_block = self.extract_title_block(image)
            title_block_text = self.extract_text_from_image(title_block, focus_on_title_block=False)
            full_text = self.extract_text_from_image(image, focus_on_title_block=False)

            # Combine texts for better coverage
            combined_text = f"{title_block_text} {full_text}"

            # Find information using both texts
            results["scale"] = self.find_scale(combined_text)
            results["paper_size"] = self.find_paper_size(combined_text) or "A3"
            results["drawing_number"] = self.find_drawing_number(combined_text)

            # Additional validation
            if not results["scale"]:
                common_scales = ["1:50", "1:100", "1:200"]
                for scale in common_scales:
                    if scale in combined_text.replace(" ", ""):
                        results["scale"] = scale
                        break

            if not results["drawing_number"]:
                # Look for any pattern that matches M\d+/\d+/\d+
                match = re.search(r'M\d+/\d+/\d+', combined_text)
                if match:
                    results["drawing_number"] = match.group(0)

            return results

        except Exception as e:
            print(f"Error in extract_info_from_pdf: {str(e)}")
            return {
                "error": str(e),
                "scale": None,
                "paper_size": None,
                "drawing_number": None
            }
   
    def process_directory(self, directory_path):
        """Process all PDFs in a directory."""
        directory = Path(directory_path)
        pdf_files = list(directory.glob("*.pdf"))
       
        if not pdf_files:
            return {"error": "No PDF files found in the directory"}
       
        results = []
        for pdf_file in pdf_files:
            print(f"Processing {pdf_file}")
            result = self.extract_info_from_pdf(str(pdf_file))
            results.append(result)
       
        return results

def extract_text(pdf_key, temp_dir=None):
    """
    Extract text and metadata from a PDF file.
    
    Args:
        pdf_key (str): S3 key of the PDF file
        temp_dir (str, optional): Directory to store temporary files
        
    Returns:
        dict: Extracted text and metadata
    """
    temp_pdf_path = None
    doc = None
    
    try:
        if temp_dir is None:
            temp_dir = tempfile.mkdtemp()
            
        temp_pdf_path = os.path.join(temp_dir, os.path.basename(pdf_key))
        
        # Download PDF from S3
        s3_client.download_file(S3_BUCKET_NAME, pdf_key, temp_pdf_path)
        
        # Open the PDF
        doc = fitz.open(temp_pdf_path)
        page = doc[0]  # Process first page
        
        # Convert page to image at higher resolution
        zoom = 2  # Increase zoom to get better resolution
        mat = fitz.Matrix(zoom, zoom)
        pix = page.get_pixmap(matrix=mat, alpha=False)  # Remove alpha to ensure RGB
        
        # Convert to PIL Image first
        img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
        
        # Convert to numpy array for OpenCV
        img_np = np.array(img)
        
        # Run OCR
        result = ocr.ocr(img_np, cls=True)
        
        # Extract text and metadata
        extracted_text = []
        scale = None
        paper_size = None
        drawing_number = None
        
        if result and result[0]:
            for line in result[0]:
                if line:
                    text = line[1][0]
                    confidence = line[1][1]
                    if confidence > 0.3:
                        extracted_text.append(text)
                        
                        # Look for scale information
                        text_lower = text.lower()
                        if "scale" in text_lower and not scale:
                            scale = text
                        elif "1:" in text_lower and not scale:
                            scale = text
                        
                        # Look for paper size
                        if ("a0" in text_lower or "a1" in text_lower or 
                            "a2" in text_lower or "a3" in text_lower or 
                            "a4" in text_lower) and not paper_size:
                            paper_size = text
                        
                        # Look for drawing number
                        if (any(pattern in text_lower for pattern in 
                            ["drawing", "dwg", "drg", "no.", "number"]) and 
                            not drawing_number):
                            drawing_number = text
        
        return {
            "scale": scale,
            "paper_size": paper_size,
            "drawing_number": drawing_number,
            "extracted_text": extracted_text
        }
        
    except Exception as e:
        print(f"Error extracting text from PDF {pdf_key}: {str(e)}")
        return {
            "error": str(e),
            "scale": None,
            "paper_size": None,
            "drawing_number": None,
            "extracted_text": []
        }
    finally:
        # Cleanup
        try:
            if doc:
                doc.close()
            if temp_pdf_path and os.path.exists(temp_pdf_path):
                os.unlink(temp_pdf_path)
        except Exception as e:
            print(f"Error cleaning up PDF file: {str(e)}")
 