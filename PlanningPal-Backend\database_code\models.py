from sqlalchemy import Column, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateTime, BigInteger, Float, Text, Enum, JSON
from sqlalchemy.orm import relationship
from database_code.database import Base
from datetime import datetime, UTC

# ------------------------ USERS TABLE ------------------------
class Users(Base):
    __tablename__ = "users"

    id = Column(BigInteger, primary_key=True, index=True, autoincrement=True)
    first_name = Column(String(255), nullable=False)
    last_name = Column(String(255), nullable=False)
    phone_number = Column(String(15), nullable=True)
    age = Column(Integer, nullable=True)
    gender = Column(Enum('male', 'female', 'other'), nullable=True)
    postal_code = Column(String(20), nullable=True)
    county = Column(String(100), nullable=True)
    address = Column(String(255), nullable=True)
    email = Column(String(100), unique=True, nullable=False)
    passwords = Column(String(255), nullable=False)
    role = Column(Enum('user', 'planner', 'admin'), default='user')
    is_verified = Column(String(3), default='no')
    created_at = Column(DateTime, default=datetime.utcnow)

    sessions = relationship("UserSessions", back_populates="user")

# ------------------------ USER SESSIONS TABLE ------------------------
class UserSessions(Base):
    __tablename__ = "user_sessions"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, ForeignKey("users.id"), nullable=False)
    refresh_token = Column(String(255), nullable=False)
    access_token = Column(String(255), nullable=True)
    last_active = Column(DateTime, default=datetime.utcnow, nullable=False)

    user = relationship("Users", back_populates="sessions")

# ------------------------ COUNCIL DETAILS TABLE ------------------------
class CouncilDetails(Base):
    __tablename__ = "council_details"

    council_id = Column(BigInteger, primary_key=True, autoincrement=True)
    reference_guide_path = Column(String(255), nullable=False)
    council_name = Column(String(50), nullable=False)
    council_type = Column(String(50), nullable=False)

# ------------------------ APPLICATION TYPES TABLE ------------------------
class ApplicationTypes(Base):
    __tablename__ = "application_types"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    kind = Column(String(50), nullable=False)
    reference_guide_path = Column(String(255), nullable=False)

# ------------------------ COUNCIL APPLICATION MAP TABLE ------------------------
class CouncilApplicationMap(Base):
    __tablename__ = "council_application_map"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    council_id = Column(BigInteger, ForeignKey("council_details.council_id"), nullable=False)
    application_id = Column(BigInteger, ForeignKey("application_types.id"), nullable=False)

    # Relationships
    council = relationship("CouncilDetails")
    application = relationship("ApplicationTypes")

# ------------------------ USER APPLICATION DETAILS TABLE ------------------------
class UserApplicationDetails(Base):
    __tablename__ = "user_application_details"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, ForeignKey("users.id"), nullable=False)
    application_id = Column(BigInteger, ForeignKey("council_application_map.id"), nullable=False)
    stature = Column(BigInteger, ForeignKey("user_application_status.id"), default=0)
    application_name = Column(String(255), nullable=False)
    site_post_code = Column(String(50), nullable=False)
    site_address = Column(String(255), nullable=False)
    validation_timestamp = Column(DateTime, default=datetime.now(UTC))
    approval_timestamp = Column(DateTime, default=datetime.now(UTC))

    # Relationships
    user = relationship("Users")
    application = relationship("CouncilApplicationMap")
    status = relationship("UserApplicationStatus")

# ------------------------ OAUTH CLIENTS TABLE ------------------------
class OauthClients(Base):
    __tablename__ = "oauth_clients"

    client_id = Column(String(100), primary_key=True)
    user_id = Column(BigInteger, ForeignKey("users.id"), nullable=True)
    redirect_uri = Column(String(255), nullable=False)
    grant_type = Column(String(50), nullable=False)
    response_type = Column(String(50), nullable=False)
    scope = Column(String(255), nullable=True)

    user = relationship("Users")

# ------------------------ OAUTH TOKENS TABLE ------------------------
class OauthTokens(Base):
    __tablename__ = "oauth_tokens"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    access_token = Column(String(255), unique=True, nullable=False)
    refresh_token = Column(String(255), unique=True, nullable=False)
    expires_at = Column(DateTime, nullable=False)
    scope = Column(String(255), nullable=True)
    client_id = Column(String(100), ForeignKey("oauth_clients.client_id"), nullable=False)
    user_id = Column(BigInteger, ForeignKey("users.id"), nullable=True)

    client = relationship("OauthClients")
    user = relationship("Users")

# ------------------------ APPLICATION DOCUMENT MAP TABLE ------------------------
class ApplicationDocMap(Base):
    __tablename__ = "application_doc_map"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    application_id = Column(BigInteger, ForeignKey("council_application_map.id"), nullable=False)
    document_name = Column(String(50), nullable=False)
    weightage = Column(Integer, nullable=False)
    sample_document_path = Column(String(255), nullable=False)

    application = relationship("CouncilApplicationMap")

# ------------------------ USER APPLICATION STATUS TABLE ------------------------
class UserApplicationStatus(Base):
    __tablename__ = "user_application_status"

    id = Column(BigInteger, primary_key=True)
    applicant_status = Column(String(255), nullable=True)
    admin_status = Column(String(255), nullable=True)
    planner_status = Column(String(255), nullable=True)

# ------------------------ USER APPLICATION DOC MAP TABLE ------------------------
class UserApplicationDocMap(Base):
    __tablename__ = "user_application_doc_map"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_application_id = Column(BigInteger, ForeignKey("user_application_details.id"), nullable=False)
    document_path = Column(String(255), nullable=False)

    user_application = relationship("UserApplicationDetails")

# ------------------------ SECTION MASTER TABLE ------------------------
class SectionMaster(Base):
    __tablename__ = "section_master"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    application_type = Column(BigInteger, ForeignKey("council_application_map.id"), nullable=False)
    name = Column(String(50), nullable=False)
    weightage = Column(Float, nullable=False, default=0.0)

    application = relationship("CouncilApplicationMap")

# ------------------------ TYPE MASTER TABLE ------------------------
class TypeMaster(Base):
    __tablename__ = "type_master"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    application_type = Column(BigInteger, ForeignKey("council_application_map.id"), nullable=False)
    name = Column(String(50), nullable=False)
    weightage = Column(Float, nullable=False, default=0.0)

    application = relationship("CouncilApplicationMap")

# ------------------------ FIELD MASTER TABLE ------------------------
class FieldMaster(Base):
    __tablename__ = "field_master"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    application_type = Column(BigInteger, ForeignKey("council_application_map.id"), nullable=False)
    section = Column(BigInteger, ForeignKey("section_master.id"), nullable=False)
    type = Column(BigInteger, ForeignKey("type_master.id"), nullable=False)
    name = Column(Text, nullable=False)
    weightage = Column(Float, nullable=False, default=0.0)
    is_optional = Column(Boolean, default=False)

    application = relationship("CouncilApplicationMap")
    section_relation = relationship("SectionMaster")
    type_relation = relationship("TypeMaster")

# ------------------------ USER INPUTS TABLE ------------------------
class UserInputs(Base):
    __tablename__ = "user_inputs"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_application_id = Column(BigInteger, ForeignKey("user_application_details.id"), nullable=False)
    field = Column(BigInteger, ForeignKey("field_master.id"), nullable=False)
    user_input = Column(Text, nullable=False)
    validation_score = Column(Float, nullable=False, default=0.0)

    user_application = relationship("UserApplicationDetails")
    field_relation = relationship("FieldMaster")

# ------------------------ CHAT RECORDS TABLE ------------------------
class ChatRecord(Base):
    __tablename__ = "chat_records"

    id = Column(Integer, primary_key=True, autoincrement=True)
    question = Column(Text, nullable=False)
    answer = Column(Text, nullable=False)
    feedback = Column(String(10), nullable=True)  # "yes", "no", or None
    timestamp = Column(DateTime, default=datetime.now(UTC))

    def __repr__(self):
        return f"<ChatRecord id={self.id} question='{self.question[:30]}...' feedback={self.feedback}>"

class BNGHabitatScores(Base):
    __tablename__ = "bng_habitat_scores"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    habitat_type = Column(String(255), nullable=False)
    broad_habitat_group = Column(String(255), nullable=False)
    strategic_significance = Column(String(255), nullable=False)
    ss_score = Column(Float, nullable=False)
    distinctiveness_score = Column(Float, nullable=False)
    habitat_score = Column(Float, nullable=False)

class HabitatMetricsSummary(Base):
    __tablename__ = "habitat_metrics_summary"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    application_id = Column(BigInteger, ForeignKey("user_application_details.id"), nullable=False)
    row_id = Column(BigInteger, nullable=False)  # Newly added row_id
    score_entry = Column(BigInteger, ForeignKey("bng_habitat_scores.id"), nullable=True)  # new column added
    total_area = Column(Float, nullable=False)
    area_retained = Column(Float, nullable=False)
    area_enhanced = Column(Float, nullable=False)
    total_habitat_units_onsite = Column(Float, nullable=False)
    area_lost = Column(Float, nullable=False)
    units_lost = Column(Float, nullable=False)

    user_application = relationship("UserApplicationDetails")
    score_relation = relationship("BNGHabitatScores")  # optional: if you want access to score details

class PreFlightMaster(Base):
    __tablename__ = "pre_flight_master"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    
    user_application_id = Column(BigInteger, ForeignKey("user_application_details.id", ondelete="CASCADE"), nullable=False)
    pre_flight_score = Column(JSON, nullable=False)
    suggested_edits = Column(JSON, nullable=True)
    is_updated = Column(Boolean, default=False)
    comments = Column(String(255), nullable=True)
    preview_doc = Column(Text, nullable=True)

    # Optional: define relationship to user_application_details if needed
    user_application = relationship("UserApplicationDetails")

class CouncilComments(Base):
    __tablename__ = "council_comments"

    id = Column(BigInteger, primary_key=True, autoincrement=True)

    version_str = Column(String(50), nullable=False)
    user_application_id = Column(BigInteger, ForeignKey("user_application_details.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    comments = Column(Text, nullable=False)

    user_application = relationship("UserApplicationDetails")
    user = relationship("Users")