# PlanningPal Chatbot API

A sophisticated RAG (Retrieval-Augmented Generation) based chatbot API designed to assist users with householder planning applications for Newark and Sherwood District Council.

## Core Technologies

- **Vector Database**: Qdrant
- **Embeddings**: Amazon Bedrock (Titan Embed Text v1)
- **LLM**: Meta Llama 3 70B (via Amazon Bedrock)
- **Framework**: FastAPI
- **Database**: SQL (via SQLAlchemy)

## Key Components

### 1. Vector Database Collections
- `primary_docs_kb`: Main knowledge base for planning documents
- `faqs_collection`: Collection of frequently asked questions

### 2. Document Processing
- **Primary Documents**: Located in `Primary_Documents/` directory
- **FAQs**: Stored in `FAQs.json` with structured metadata
- **Document Types**: PDF files containing planning application information

### 3. Core Functions

#### Document Management
- `create_kb()`: Creates knowledge base from scratch
- `setup_qdrant_kb()`: Initializes or updates Qdrant knowledge base
- `incremental_reindex()`: Updates vector store with new/updated documents
- `embed_new_pdfs()`: Processes and embeds new PDF documents

#### Query Processing
- `handle_query()`: Main function for processing user queries
- `search_faqs()`: Fuzzy matching for FAQ retrieval
- `correct_typos()`: Typo correction using LLM
- `detect_sentiment()`: Sentiment analysis of user queries

#### Vector Store Operations
- `create_vectorstore()`: Initializes Qdrant vector store
- `initialize_llm()`: Sets up LLM with vector store
- `load_faqs_to_qdrant()`: Loads FAQs into Qdrant database

### 4. API Endpoints

- `POST /ask`: Process user questions
- `POST /feedback`: Submit feedback on responses
- `POST /reindex`: Re-index documents
- `POST /clear_chat`: Reset conversation history

### 5. Features

- **RAG Implementation**: Combines retrieval and generation for accurate responses
- **Sentiment Analysis**: Detects user sentiment for appropriate responses
- **FAQ Integration**: Quick access to common questions
- **Chat History**: Maintains conversation context
- **Feedback System**: Allows users to rate responses
- **Document Management**: Automatic processing of new/updated documents

### 6. Environment Variables

Required environment variables:
- `AWS_REGION`: AWS region for Bedrock services
- `AWS_ACCESS_KEY`: AWS access key
- `AWS_SECRET_KEY`: AWS secret key
- `SMTP_SENDER_EMAIL`: Email for notifications

### 7. Response Formatting

The system formats responses with specific guidelines:
- Maximum 35-40 characters per line
- Manual line breaks for readability
- Structured bullet points and numbered lists
- Multiple paragraphs with spacing
- Special handling for greetings and unavailable information

### 8. Error Handling

- Graceful handling of missing documents
- Error recovery for failed embeddings
- Feedback validation
- Query validation